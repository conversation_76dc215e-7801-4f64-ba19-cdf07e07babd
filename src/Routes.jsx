import React from "react";
import {
  BrowserRouter,
  Routes as RouterRoutes,
  Route,
  Navigate,
} from "react-router-dom";
import ScrollToTop from "components/ScrollToTop";
import ErrorBoundary from "components/ErrorBoundary";
import NotFound from "pages/NotFound";

// Authentication components
import LoginPage from "pages/login";
import AdminDashboard from "pages/admin";
import ProtectedRoute from "components/auth/ProtectedRoute";
import AuthRedirect from "components/auth/AuthRedirect";

// Page imports
import ProfessionSelectionLanding from "pages/profession-selection-landing";
import ServiceProfileCollection from "pages/service-profile-collection";
import RiskAssessmentQuestionnaire from "pages/risk-assessment-questionnaire";
import DynamicResultsDashboard from "pages/dynamic-results-dashboard";
import GapCalculatorTool from "pages/gap-calculator-tool";
import ReportDeliveryConfirmation from "pages/report-delivery-confirmation";
import PrivacyPolicy from "pages/privacy-policy";
import TermsOfService from "pages/terms-of-service";
import Disclosures from "pages/disclosures";
import Contact from "pages/contact";

const Routes = () => {
  return (
    <BrowserRouter>
      <ErrorBoundary>
        <ScrollToTop />
        <RouterRoutes>
          {/* Authentication routes */}
          <Route
            path="/login"
            element={
              <AuthRedirect>
                <LoginPage />
              </AuthRedirect>
            }
          />

          {/* Admin routes */}
          <Route
            path="/admin"
            element={
              <ProtectedRoute requireAdmin={true}>
                <AdminDashboard />
              </ProtectedRoute>
            }
          />

          {/* Protected main application routes */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <ProfessionSelectionLanding />
              </ProtectedRoute>
            }
          />
          <Route
            path="/profession-selection-landing"
            element={
              <ProtectedRoute>
                <ProfessionSelectionLanding />
              </ProtectedRoute>
            }
          />
          <Route
            path="/service-profile-collection"
            element={
              <ProtectedRoute>
                <ServiceProfileCollection />
              </ProtectedRoute>
            }
          />
          <Route
            path="/risk-assessment-questionnaire"
            element={
              <ProtectedRoute>
                <RiskAssessmentQuestionnaire />
              </ProtectedRoute>
            }
          />
          <Route
            path="/dynamic-results-dashboard"
            element={
              <ProtectedRoute>
                <DynamicResultsDashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="/gap-calculator-tool"
            element={
              <ProtectedRoute>
                <GapCalculatorTool />
              </ProtectedRoute>
            }
          />
          <Route
            path="/report-delivery-confirmation"
            element={
              <ProtectedRoute>
                <ReportDeliveryConfirmation />
              </ProtectedRoute>
            }
          />

          {/* Public routes */}
          <Route path="/privacy" element={<PrivacyPolicy />} />
          <Route path="/terms" element={<TermsOfService />} />
          <Route path="/disclosures" element={<Disclosures />} />
          <Route path="/contact" element={<Contact />} />

          {/* Catch-all route for 404 pages */}
          <Route path="/404" element={<NotFound />} />
          <Route path="*" element={<Navigate to="/404" replace />} />
        </RouterRoutes>
      </ErrorBoundary>
    </BrowserRouter>
  );
};

export default Routes;
