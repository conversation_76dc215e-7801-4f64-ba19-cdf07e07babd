import { supabase } from "./supabase";

// Authentication functions using Supabase Auth
export const signUp = async (email, password, userData = {}) => {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData,
      },
    });

    if (error) throw error;

    return { user: data.user, session: data.session, error: null };
  } catch (error) {
    console.error("Sign up error:", error);
    return { user: null, session: null, error: error.message };
  }
};

export const signIn = async (email, password) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;

    return { user: data.user, session: data.session, error: null };
  } catch (error) {
    console.error("Sign in error:", error);
    return { user: null, session: null, error: error.message };
  }
};

export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error("Sign out error:", error);
    return { error: error.message };
  }
};

export const getCurrentSession = async () => {
  try {
    const {
      data: { session },
      error,
    } = await supabase.auth.getSession();
    if (error) throw error;
    return { session, error: null };
  } catch (error) {
    console.error("Get session error:", error);
    return { session: null, error: error.message };
  }
};

export const getCurrentUser = async () => {
  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();
    if (error) throw error;
    return { user, error: null };
  } catch (error) {
    console.error("Get user error:", error);
    return { user: null, error: error.message };
  }
};
// User profile functions
export const getUserProfile = async (userId) => {
  try {
    const { data, error } = await supabase
      .from("user_profiles")
      .select("*")
      .eq("id", userId)
      .single();

    if (error) throw error;
    return { profile: data, error: null };
  } catch (error) {
    console.error("Get user profile error:", error);
    return { profile: null, error: error.message };
  }
};

export const updateUserProfile = async (userId, updates) => {
  try {
    const { data, error } = await supabase
      .from("user_profiles")
      .update(updates)
      .eq("id", userId)
      .select()
      .single();

    if (error) throw error;
    return { profile: data, error: null };
  } catch (error) {
    console.error("Update user profile error:", error);
    return { profile: null, error: error.message };
  }
};

// Admin functions
export const getAllUserProfiles = async () => {
  try {
    const { data, error } = await supabase
      .from("user_profiles")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) throw error;
    return { profiles: data, error: null };
  } catch (error) {
    console.error("Get all user profiles error:", error);
    return { profiles: [], error: error.message };
  }
};

export const createUserWithProfile = async (email, password, role = "user") => {
  try {
    // Create user via Supabase Auth
    const { data: authData, error: authError } =
      await supabase.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
      });

    if (authError) throw authError;

    // Update the automatically created profile with the correct role
    const { data: profileData, error: profileError } = await supabase
      .from("user_profiles")
      .update({ role })
      .eq("id", authData.user.id)
      .select()
      .single();

    if (profileError) throw profileError;

    return { user: authData.user, profile: profileData, error: null };
  } catch (error) {
    console.error("Create user with profile error:", error);
    return { user: null, profile: null, error: error.message };
  }
};
export const deleteUser = async (userId) => {
  try {
    // Delete user via Supabase Auth Admin API
    const { error } = await supabase.auth.admin.deleteUser(userId);
    if (error) throw error;

    return { error: null };
  } catch (error) {
    console.error("Delete user error:", error);
    return { error: error.message };
  }
};

export const updateUserRole = async (userId, role) => {
  try {
    const { data, error } = await supabase
      .from("user_profiles")
      .update({ role })
      .eq("id", userId)
      .select()
      .single();

    if (error) throw error;
    return { profile: data, error: null };
  } catch (error) {
    console.error("Update user role error:", error);
    return { profile: null, error: error.message };
  }
};

export const toggleUserStatus = async (userId, isActive) => {
  try {
    const { data, error } = await supabase
      .from("user_profiles")
      .update({ is_active: isActive })
      .eq("id", userId)
      .select()
      .single();

    if (error) throw error;
    return { profile: data, error: null };
  } catch (error) {
    console.error("Toggle user status error:", error);
    return { profile: null, error: error.message };
  }
};

// Utility functions
export const isAuthenticated = (session) => {
  return session && session.user;
};

export const isAdmin = (profile) => {
  return profile && profile.role === "admin";
};

export const resetPassword = async (email) => {
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });

    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error("Reset password error:", error);
    return { error: error.message };
  }
};
