import bcrypt from "bcryptjs";
import { supabase } from "./supabase";
import {
  validateEmail,
  validatePassword,
  sanitizeInput,
} from "../utils/validation";
import {
  handleAuthError,
  handleDatabaseError,
  logError,
} from "../utils/errorHandler";

// Password utilities
export const hashPassword = async (password) => {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
};

export const verifyPassword = async (password, hash) => {
  return await bcrypt.compare(password, hash);
};

// Authentication functions
export const signInWithEmailPassword = async (email, password) => {
  try {
    // Validate inputs
    const emailError = validateEmail(email);
    if (emailError) {
      throw new Error(emailError);
    }

    const passwordError = validatePassword(password);
    if (passwordError) {
      throw new Error(passwordError);
    }

    // Sanitize email input
    const sanitizedEmail = sanitizeInput(email.toLowerCase());

    // Get user from our custom users table
    const { data: user, error: userError } = await supabase
      .from("users")
      .select("*")
      .eq("email", sanitizedEmail)
      .eq("is_active", true)
      .single();

    if (userError || !user) {
      throw new Error("Invalid email or password");
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, user.password_hash);
    if (!isValidPassword) {
      throw new Error("Invalid email or password");
    }

    // Create a session token (simplified - in production use proper JWT)
    const sessionData = {
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    };

    // Store session in localStorage (in production, use httpOnly cookies)
    localStorage.setItem("auth_session", JSON.stringify(sessionData));

    return { user: sessionData.user, error: null };
  } catch (error) {
    logError(error, "signInWithEmailPassword");
    return { user: null, error: handleAuthError(error) };
  }
};

export const signOut = () => {
  localStorage.removeItem("auth_session");
  window.location.href = "/login";
};

export const getCurrentUser = () => {
  try {
    const session = localStorage.getItem("auth_session");
    if (!session) return null;

    const sessionData = JSON.parse(session);

    // Check if session is expired
    if (new Date() > new Date(sessionData.expires)) {
      localStorage.removeItem("auth_session");
      return null;
    }

    return sessionData.user;
  } catch (error) {
    localStorage.removeItem("auth_session");
    return null;
  }
};

export const isAuthenticated = () => {
  return getCurrentUser() !== null;
};

export const isAdmin = () => {
  const user = getCurrentUser();
  return user && user.role === "admin";
};

// User management functions (admin only)
export const createUser = async (userData) => {
  try {
    const hashedPassword = await hashPassword(userData.password);

    const { data, error } = await supabase
      .from("users")
      .insert([
        {
          email: userData.email,
          password_hash: hashedPassword,
          role: userData.role || "user",
        },
      ])
      .select()
      .single();

    if (error) throw error;
    return { user: data, error: null };
  } catch (error) {
    return { user: null, error: error.message };
  }
};

export const updateUser = async (userId, updates) => {
  try {
    // If password is being updated, hash it
    if (updates.password) {
      updates.password_hash = await hashPassword(updates.password);
      delete updates.password;
    }

    const { data, error } = await supabase
      .from("users")
      .update(updates)
      .eq("id", userId)
      .select()
      .single();

    if (error) throw error;
    return { user: data, error: null };
  } catch (error) {
    return { user: null, error: error.message };
  }
};

export const deleteUser = async (userId) => {
  try {
    const { error } = await supabase.from("users").delete().eq("id", userId);

    if (error) throw error;
    return { error: null };
  } catch (error) {
    return { error: error.message };
  }
};

export const getAllUsers = async () => {
  try {
    const { data, error } = await supabase
      .from("users")
      .select("id, email, role, created_at, updated_at, is_active")
      .order("created_at", { ascending: false });

    if (error) throw error;
    return { users: data, error: null };
  } catch (error) {
    return { users: [], error: error.message };
  }
};

export const toggleUserStatus = async (userId, isActive) => {
  try {
    const { data, error } = await supabase
      .from("users")
      .update({ is_active: isActive })
      .eq("id", userId)
      .select()
      .single();

    if (error) throw error;
    return { user: data, error: null };
  } catch (error) {
    return { user: null, error: error.message };
  }
};
