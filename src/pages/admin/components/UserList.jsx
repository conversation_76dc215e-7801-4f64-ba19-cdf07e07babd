import React, { useState } from 'react';
import { toggleUserStatus, deleteUser } from '../../../lib/auth';

const UserList = ({ users, onEditUser, onRefresh }) => {
  const [loading, setLoading] = useState({});

  const handleToggleStatus = async (userId, currentStatus) => {
    setLoading(prev => ({ ...prev, [userId]: true }));
    
    const { error } = await toggleUserStatus(userId, !currentStatus);
    if (error) {
      alert('Error updating user status: ' + error);
    } else {
      onRefresh();
    }
    
    setLoading(prev => ({ ...prev, [userId]: false }));
  };

  const handleDeleteUser = async (userId, userEmail) => {
    if (!confirm(`Are you sure you want to delete user ${userEmail}? This action cannot be undone.`)) {
      return;
    }

    setLoading(prev => ({ ...prev, [userId]: true }));
    
    const { error } = await deleteUser(userId);
    if (error) {
      alert('Error deleting user: ' + error);
    } else {
      onRefresh();
    }
    
    setLoading(prev => ({ ...prev, [userId]: false }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
      <table className="min-w-full divide-y divide-gray-300">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              User
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Role
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Created
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {users.map((user) => (
            <tr key={user.id}>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm font-medium text-gray-900">{user.email}</div>
                <div className="text-sm text-gray-500">ID: {user.id.slice(0, 8)}...</div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  user.role === 'admin' 
                    ? 'bg-purple-100 text-purple-800' 
                    : 'bg-green-100 text-green-800'
                }`}>
                  {user.role}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  user.is_active 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {user.is_active ? 'Active' : 'Inactive'}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {formatDate(user.created_at)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button
                  onClick={() => onEditUser(user)}
                  className="text-blue-600 hover:text-blue-900"
                  disabled={loading[user.id]}
                >
                  Edit
                </button>
                <button
                  onClick={() => handleToggleStatus(user.id, user.is_active)}
                  className={`${
                    user.is_active 
                      ? 'text-red-600 hover:text-red-900' 
                      : 'text-green-600 hover:text-green-900'
                  }`}
                  disabled={loading[user.id]}
                >
                  {loading[user.id] ? 'Loading...' : (user.is_active ? 'Deactivate' : 'Activate')}
                </button>
                <button
                  onClick={() => handleDeleteUser(user.id, user.email)}
                  className="text-red-600 hover:text-red-900"
                  disabled={loading[user.id]}
                >
                  Delete
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      
      {users.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No users found.
        </div>
      )}
    </div>
  );
};

export default UserList;
