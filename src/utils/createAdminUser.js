// Utility script to create initial admin user
// This should be run once to set up the initial admin account

import { signUp, updateUserRole } from '../lib/auth';

export const createInitialAdmin = async () => {
  try {
    console.log('Creating initial admin user...');
    
    // Create the admin user
    const { user, session, error } = await signUp(
      '<EMAIL>',
      'admin@96',
      { role: 'admin' }
    );

    if (error) {
      console.error('Error creating admin user:', error);
      return { success: false, error };
    }

    if (user) {
      // Update the user profile to admin role
      const { profile, error: roleError } = await updateUserRole(user.id, 'admin');
      
      if (roleError) {
        console.error('Error setting admin role:', roleError);
        return { success: false, error: roleError };
      }

      console.log('Admin user created successfully:', user.email);
      return { success: true, user, profile };
    }

    return { success: false, error: 'User creation failed' };
  } catch (error) {
    console.error('Unexpected error:', error);
    return { success: false, error: error.message };
  }
};

// Function to be called from browser console for initial setup
window.createInitialAdmin = createInitialAdmin;
