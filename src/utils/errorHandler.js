// Error handling utilities

export const handleAuthError = (error) => {
  // Log error for debugging (in production, send to logging service)
  console.error('Authentication error:', error);
  
  // Return user-friendly error messages
  if (error.message?.includes('Invalid email or password')) {
    return 'Invalid email or password. Please try again.';
  }
  
  if (error.message?.includes('duplicate key value')) {
    return 'An account with this email already exists.';
  }
  
  if (error.message?.includes('permission denied')) {
    return 'You do not have permission to perform this action.';
  }
  
  if (error.message?.includes('network')) {
    return 'Network error. Please check your connection and try again.';
  }
  
  // Generic error message for unknown errors
  return 'An unexpected error occurred. Please try again later.';
};

export const handleDatabaseError = (error) => {
  console.error('Database error:', error);
  
  if (error.code === '23505') { // Unique constraint violation
    return 'This email is already registered.';
  }
  
  if (error.code === '23503') { // Foreign key constraint violation
    return 'Cannot delete this record due to existing dependencies.';
  }
  
  if (error.code === '42501') { // Insufficient privilege
    return 'You do not have permission to perform this action.';
  }
  
  return 'Database error occurred. Please try again.';
};

export const logError = (error, context = '') => {
  const errorInfo = {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href
  };
  
  // In production, send to logging service
  console.error('Error logged:', errorInfo);
  
  // Could integrate with services like Sentry, LogRocket, etc.
  // Example: Sentry.captureException(error, { extra: errorInfo });
};
