// Input validation utilities

export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!email) {
    return 'Email is required';
  }
  if (!emailRegex.test(email)) {
    return 'Please enter a valid email address';
  }
  return null;
};

export const validatePassword = (password) => {
  if (!password) {
    return 'Password is required';
  }
  if (password.length < 6) {
    return 'Password must be at least 6 characters long';
  }
  if (password.length > 128) {
    return 'Password must be less than 128 characters';
  }
  return null;
};

export const validateRole = (role) => {
  const validRoles = ['admin', 'user'];
  if (!role) {
    return 'Role is required';
  }
  if (!validRoles.includes(role)) {
    return 'Invalid role selected';
  }
  return null;
};

export const sanitizeInput = (input) => {
  if (typeof input !== 'string') {
    return input;
  }
  
  // Remove potentially dangerous characters
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, ''); // Remove event handlers
};

export const validateUserData = (userData) => {
  const errors = {};
  
  const emailError = validateEmail(userData.email);
  if (emailError) errors.email = emailError;
  
  if (userData.password) {
    const passwordError = validatePassword(userData.password);
    if (passwordError) errors.password = passwordError;
  }
  
  if (userData.role) {
    const roleError = validateRole(userData.role);
    if (roleError) errors.role = roleError;
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};
